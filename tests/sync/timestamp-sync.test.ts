import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { TFile } from 'obsidian';
import type { GhostPost } from '../../src/types';

// Mock the sync status view classes
class MockSyncStatusView {
  currentFile: TFile | null = null;
  syncStatus: any = {};
  plugin: any = {};
  app: any = {};

  constructor() {
    this.plugin = {
      settings: { verbose: false },
      syncCurrentPostToGhost: vi.fn().mockResolvedValue(undefined)
    };
  }

  async syncFromGhost() {
    // Mock implementation
    return Promise.resolve();
  }

  updateSyncStatus() {
    // Mock implementation
  }

  // Test the smart sync logic directly
  async performSmartSync() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      throw new Error('No Ghost post to sync');
    }

    const ghostPost = this.syncStatus.ghostPost;

    // Use actual file modification time instead of frontmatter updated_at
    const fileModificationTime = this.currentFile.stat?.mtime || 0;
    const ghostUpdatedAt = new Date(ghostPost.updated_at);
    const ghostUpdatedAtMs = ghostUpdatedAt.getTime();

    if (this.plugin.settings.verbose) {
      console.log('Smart sync comparison:');
      console.log('File modification time:', new Date(fileModificationTime).toISOString());
      console.log('Ghost updated_at:', ghostPost.updated_at);
      console.log('Time difference (ms):', Math.abs(fileModificationTime - ghostUpdatedAtMs));
    }

    // Return the sync decision for testing
    if (ghostPost.status !== 'draft') {
      return 'sync_from_ghost_published';
    }

    // Add tolerance for small time differences (1 second = 1000ms)
    const timeDifference = Math.abs(ghostUpdatedAtMs - fileModificationTime);
    const tolerance = 1000; // 1 second tolerance

    if (timeDifference <= tolerance) {
      return 'bidirectional_sync';
    }
    else if (ghostUpdatedAtMs > fileModificationTime) {
      return 'sync_from_ghost_newer';
    }
    else {
      return 'sync_to_ghost_newer';
    }
  }
}

describe('Timestamp-based Sync Logic', () => {
  let syncView: MockSyncStatusView;
  let mockFile: TFile;
  let mockGhostPost: GhostPost;

  beforeEach(() => {
    vi.clearAllMocks();
    syncView = new MockSyncStatusView();

    // Create mock file with stat
    mockFile = {
      stat: {
        mtime: Date.now(),
        ctime: Date.now(),
        size: 1000
      },
      path: 'test.md',
      name: 'test.md',
      basename: 'test',
      extension: 'md'
    } as TFile;

    // Create mock Ghost post
    mockGhostPost = {
      id: 'ghost-id',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft',
      updated_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      published_at: null,
      html: '<p>Test content</p>',
      lexical: null,
      tags: [],
      authors: [],
      featured: false,
      feature_image: null,
      visibility: 'public',
      email: null
    } as GhostPost;

    syncView.currentFile = mockFile;
    syncView.syncStatus = { ghostPost: mockGhostPost };
  });

  describe('Published Post Priority', () => {
    it('should sync from Ghost when post is published', async () => {
      mockGhostPost.status = 'published';

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_published');
    });

    it('should sync from Ghost when post is sent', async () => {
      mockGhostPost.status = 'sent';

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_published');
    });
  });

  describe('Timestamp Comparison for Drafts', () => {
    it('should sync from Ghost when Ghost is newer', async () => {
      // Set file modification time to 1 hour ago
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      mockFile.stat!.mtime = oneHourAgo;

      // Set Ghost updated_at to now
      mockGhostPost.updated_at = new Date().toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should sync to Ghost when file is newer', async () => {
      // Set Ghost updated_at to 1 hour ago
      const oneHourAgo = new Date(Date.now() - (60 * 60 * 1000));
      mockGhostPost.updated_at = oneHourAgo.toISOString();

      // Set file modification time to now
      mockFile.stat!.mtime = Date.now();

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should do bidirectional sync when timestamps are equal', async () => {
      const now = Date.now();
      mockFile.stat!.mtime = now;
      mockGhostPost.updated_at = new Date(now).toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('bidirectional_sync');
    });

    it('should do bidirectional sync when timestamps are very close', async () => {
      const now = Date.now();
      mockFile.stat!.mtime = now;
      // Ghost timestamp 500ms later (within 1000ms tolerance)
      mockGhostPost.updated_at = new Date(now + 500).toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('bidirectional_sync');
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing file stat', async () => {
      mockFile.stat = undefined as any;

      // Should use 0 as fallback, making Ghost newer
      mockGhostPost.updated_at = new Date().toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should handle invalid Ghost updated_at', async () => {
      mockGhostPost.updated_at = 'invalid-date';

      // Invalid date becomes NaN, which in comparisons makes file appear newer
      const result = await syncView.performSmartSync();

      // With NaN comparison, file time > NaN is true, so sync to Ghost
      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should throw error when no current file', async () => {
      syncView.currentFile = null;

      await expect(syncView.performSmartSync()).rejects.toThrow('No Ghost post to sync');
    });

    it('should throw error when no Ghost post', async () => {
      syncView.syncStatus.ghostPost = null;

      await expect(syncView.performSmartSync()).rejects.toThrow('No Ghost post to sync');
    });
  });

  describe('Verbose Logging', () => {
    it('should log comparison details when verbose is enabled', async () => {
      syncView.plugin.settings.verbose = true;
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await syncView.performSmartSync();

      expect(consoleSpy).toHaveBeenCalledWith('Smart sync comparison:');
      expect(consoleSpy).toHaveBeenCalledWith('File modification time:', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('Ghost updated_at:', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('Time difference (ms):', expect.any(Number));

      consoleSpy.mockRestore();
    });

    it('should not log when verbose is disabled', async () => {
      syncView.plugin.settings.verbose = false;
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await syncView.performSmartSync();

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle typical editing workflow', async () => {
      // Scenario: User edits file locally after syncing from Ghost
      const baseTime = Date.now();

      // Ghost was updated 10 minutes ago
      mockGhostPost.updated_at = new Date(baseTime - (10 * 60 * 1000)).toISOString();

      // File was modified 5 minutes ago (after Ghost update)
      mockFile.stat!.mtime = baseTime - (5 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should handle Ghost CMS editing workflow', async () => {
      // Scenario: User edits post in Ghost CMS after local changes
      const baseTime = Date.now();

      // File was modified 10 minutes ago
      mockFile.stat!.mtime = baseTime - (10 * 60 * 1000);

      // Ghost was updated 5 minutes ago (after file modification)
      mockGhostPost.updated_at = new Date(baseTime - (5 * 60 * 1000)).toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should handle simultaneous editing (conflict resolution)', async () => {
      // Scenario: Very close timestamps suggest potential conflict
      const baseTime = Date.now();

      mockFile.stat!.mtime = baseTime;
      mockGhostPost.updated_at = new Date(baseTime + 100).toISOString(); // 100ms difference (within 1000ms tolerance)

      const result = await syncView.performSmartSync();

      // Should do bidirectional sync for conflict resolution
      expect(result).toBe('bidirectional_sync');
    });
  });
});
