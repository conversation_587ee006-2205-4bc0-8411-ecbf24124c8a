import { <PERSON>pace<PERSON>ea<PERSON>, TFile, Notice } from "obsidian";
import type GhostSyncPlugin from "../main";
import type { GhostPost } from "../types";
import { ContentConverter } from "../utils/content-converter";
import { SvelteView } from "../components/SvelteView";
import GhostSyncView from "../components/GhostSyncView.svelte";
import PublishDialog from "../components/PublishDialog.svelte";
import PostBrowser from "../components/PostBrowser.svelte";
import type { SyncStatusData, PublishOptions } from "../components/types";
import { ObsidianGhostAPI } from "../api/ghost-api";
import { SyncStatusService } from "../services/sync-status-service";
import { ObsidianAppAdapter } from "../services/obsidian-app-adapter";
import * as path from "path";

export const VIEW_TYPE_GHOST_SYNC_STATUS = 'ghost-sync-status';

export class SvelteSyncStatusView extends SvelteView {
  private currentFile: TFile | null = null;
  private syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };

  private publishDialog: PublishDialog | null = null;
  private postBrowser: PostBrowser | null = null;
  private syncStatusService: SyncStatusService;
  private appAdapter: ObsidianAppAdapter;

  constructor(leaf: WorkspaceLeaf, plugin: GhostSyncPlugin, syncStatusService?: SyncStatusService) {
    super(leaf, plugin);

    this.appAdapter = new ObsidianAppAdapter(this.app);

    if (syncStatusService) {
      this.syncStatusService = syncStatusService;
    } else {
      // Create default service with Ghost API
      const ghostAPI = new ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);
      this.syncStatusService = new SyncStatusService({
        ghostAPI,
        appAdapter: this.appAdapter
      });
    }
  }

  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }

  getDisplayText() {
    return 'Ghost';
  }

  getIcon() {
    return 'sync';
  }

  async onOpen() {
    await super.onOpen();

    // Listen for active file changes
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        console.log('Ghost tab: active-leaf-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file open events
    this.registerEvent(
      this.app.workspace.on('file-open', (file) => {
        console.log('Ghost tab: file-open event fired for:', file?.path);
        this.updateCurrentFile();
      })
    );

    // Listen for layout changes (more comprehensive)
    this.registerEvent(
      this.app.workspace.on('layout-change', () => {
        console.log('Ghost tab: layout-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file changes
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file === this.currentFile) {
          console.log('Ghost tab: file modified:', file.path);
          this.updateSyncStatus();
        }
      })
    );

    // Listen for editor changes (more reliable for file switching)
    this.registerEvent(
      this.app.workspace.on('editor-change', (editor, info) => {
        console.log('Ghost tab: editor-change event fired');
        this.updateCurrentFile();
      })
    );

    console.log('Ghost tab: Initial setup complete, calling updateCurrentFile');
    this.updateCurrentFile();
  }

  protected createComponent(container: HTMLElement) {
    const component = this.createSvelteComponent(
      GhostSyncView,
      container,
      {
        currentFile: this.currentFile,
        syncStatus: this.syncStatus
      }
    );

    // Listen to component events
    component.$on('smartSync', async () => {
      await this.smartSync();
    });

    component.$on('syncFromGhost', async () => {
      await this.syncFromGhost();
    });

    component.$on('publish', () => {
      this.showPublishDialog();
    });

    component.$on('browsePosts', () => {
      this.showPostBrowser();
    });

    return component;
  }

  private recreateComponent() {
    if (this.component) {
      // Destroy the old component
      this.component.$destroy();
      this.component = null;
    }

    // Create a new component with updated props
    const container = this.contentEl;
    container.empty();
    container.addClass('svelte-view-container');
    this.component = this.createComponent(container);
  }

  private updateCurrentFile() {
    const activeFile = this.app.workspace.getActiveFile();
    console.log('Ghost tab: updateCurrentFile called');
    console.log('Ghost tab: activeFile:', activeFile?.path);
    console.log('Ghost tab: currentFile:', this.currentFile?.path);

    if (activeFile !== this.currentFile) {
      console.log('Ghost tab: File changed, updating view');
      this.currentFile = activeFile;
      this.updateSyncStatus();

      // In Svelte 5, we need to recreate the component with new props
      // since $set is deprecated and bindable props should be used
      this.recreateComponent();
    } else {
      console.log('Ghost tab: No file change detected');
    }
  }

  private async updateSyncStatus() {
    console.log('Ghost tab: updateSyncStatus called');
    if (!this.currentFile) {
      console.log('Ghost tab: No current file, skipping sync status update');
      return;
    }

    console.log('Ghost tab: Updating sync status for file:', this.currentFile.path);

    try {
      // Use the service to calculate sync status
      this.syncStatus = await this.syncStatusService.calculateSyncStatus(this.currentFile);

      // In Svelte 5, recreate component with updated props
      this.recreateComponent();

    } catch (error) {
      console.error('Error updating sync status:', error);
      this.resetSyncStatus();
    }
  }



  private resetSyncStatus() {
    this.syncStatus = {
      title: 'unknown',
      slug: 'unknown',
      status: 'unknown',
      tags: 'unknown',
      featured: 'unknown',
      feature_image: 'unknown',
      visibility: 'unknown',
      primary_tag: 'unknown',
      created_at: 'unknown',
      updated_at: 'unknown',
      published_at: 'unknown',
      newsletter: 'unknown',
      email_sent: 'unknown'
    };

    this.recreateComponent();
  }

  private async smartSync() {
    if (!this.currentFile) {
      new Notice('No file selected');
      return;
    }

    try {
      // Check if post exists and is published/sent
      if (this.syncStatus.ghostPost) {
        const ghostPost = this.syncStatus.ghostPost;
        const isPublished = ghostPost.status === 'published';
        const isEmailSent = !!ghostPost.email;

        if (isPublished || isEmailSent) {
          // For published/sent posts, sync FROM Ghost to preserve published content
          await this.syncFromGhost();
        } else {
          // For drafts, use proper timestamp-based sync logic
          const content = await this.app.vault.read(this.currentFile);
          const { frontMatter } = ContentConverter.parseArticle(content);
          const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);

          const localUpdatedAt = normalizedFrontMatter.updated_at || normalizedFrontMatter['Updated At'];
          const fileModificationTime = this.currentFile.stat?.mtime || 0;
          const ghostUpdatedAtMs = new Date(ghostPost.updated_at).getTime();
          const localUpdatedAtMs = localUpdatedAt ? new Date(localUpdatedAt).getTime() : 0;

          if (this.plugin.settings.verbose) {
            console.log('Smart sync comparison:');
            console.log('Ghost updated_at:', ghostPost.updated_at);
            console.log('Local frontmatter updated_at:', localUpdatedAt);
            console.log('File modification time:', new Date(fileModificationTime).toISOString());
            console.log('Ghost vs Local updated_at diff (ms):', ghostUpdatedAtMs - localUpdatedAtMs);
            console.log('File vs Local updated_at diff (ms):', fileModificationTime - localUpdatedAtMs);
          }

          // Check if Ghost has been updated since our last sync TO Ghost
          const ghostIsNewer = ghostUpdatedAtMs > localUpdatedAtMs;

          // Check if file has been modified since our last sync TO Ghost (with tolerance)
          const fileIsNewer = fileModificationTime > (localUpdatedAtMs + 1000); // 1 second tolerance

          if (ghostIsNewer && fileIsNewer) {
            // Both have changes - conflict resolution with bidirectional sync
            new Notice('Both Ghost and local file have changes - performing conflict resolution...');
            await this.plugin.syncCurrentPostToGhost();
            setTimeout(async () => {
              await this.syncFromGhost();
              await this.updateSyncStatus();
            }, 1000);
          }
          else if (ghostIsNewer) {
            // Ghost has newer changes
            new Notice('Ghost post is newer - syncing from Ghost...');
            await this.syncFromGhost();
          }
          else if (fileIsNewer) {
            // Local file has newer changes
            new Notice('Local file is newer - syncing to Ghost...');
            await this.plugin.syncCurrentPostToGhost();
            await this.updateSyncStatus();
          }
          else {
            // No significant changes detected
            new Notice('No changes detected - files are in sync');
          }
        }
      } else {
        // No Ghost post exists, sync TO Ghost
        await this.plugin.syncCurrentPostToGhost();
        await this.updateSyncStatus();
      }
    } catch (error) {
      console.error('Error during smart sync:', error);
      new Notice('Error during sync: ' + error.message);
    }
  }

  private async syncFromGhost() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      new Notice('No Ghost post to sync from');
      return;
    }

    try {
      new Notice('Syncing from Ghost...');

      // Get current frontmatter to preserve local updated_at
      const currentContent = await this.app.vault.read(this.currentFile);
      const { frontMatter } = ContentConverter.parseArticle(currentContent);
      const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);
      const localUpdatedAt = normalizedFrontMatter.updated_at || normalizedFrontMatter['Updated At'];

      // Convert Ghost post to article format, preserving local updated_at
      const articleContent = ContentConverter.convertGhostPostToArticle(this.syncStatus.ghostPost, localUpdatedAt);

      // Update the current file (don't set mtime to preserve natural file modification tracking)
      await this.app.vault.modify(this.currentFile, articleContent);

      new Notice('Synced from Ghost successfully');
      setTimeout(() => this.updateSyncStatus(), 1000);
    } catch (error) {
      console.error('Error syncing from Ghost:', error);
      new Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }

  private showPublishDialog() {
    if (!this.syncStatus.ghostPost) {
      new Notice('No Ghost post to publish');
      return;
    }

    // Create modal container
    const modalContainer = document.body.createDiv();

    this.publishDialog = new PublishDialog({
      target: modalContainer,
      props: {
        ghostPost: this.syncStatus.ghostPost,
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.publishDialog.$on('confirm', async (event) => {
      await this.handlePublish(event.detail);
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });

    this.publishDialog.$on('cancel', () => {
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });
  }

  private showPostBrowser() {
    // Create modal container
    const modalContainer = document.body.createDiv();

    this.postBrowser = new PostBrowser({
      target: modalContainer,
      props: {
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.postBrowser.$on('select', async (event) => {
      await this.handlePostSelection(event.detail);
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });

    this.postBrowser.$on('cancel', () => {
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });
  }

  private async handlePublish(options: PublishOptions) {
    // Implementation for publishing
    new Notice(`Publishing with action: ${options.action}`);
  }

  private async handlePostSelection(post: GhostPost) {
    try {
      new Notice(`Syncing "${post.title}" from Ghost...`);

      const articleContent = ContentConverter.convertGhostPostToArticle(post);
      const filename = post.slug + '.md';
      const filePath = path.posix.join(this.plugin.settings.articlesDir, filename);

      const existingFile = this.app.vault.getAbstractFileByPath(filePath);

      if (existingFile) {
        // Get current frontmatter to preserve local updated_at
        const currentContent = await this.app.vault.read(existingFile as any);
        const { frontMatter } = ContentConverter.parseArticle(currentContent);
        const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);
        const localUpdatedAt = normalizedFrontMatter.updated_at || normalizedFrontMatter['Updated At'];

        // Convert with preserved local updated_at
        const preservedArticleContent = ContentConverter.convertGhostPostToArticle(post, localUpdatedAt);

        // Update existing file
        await this.app.vault.modify(existingFile as any, preservedArticleContent);
        new Notice(`Updated "${post.title}" in ${filePath}`);
      } else {
        // Create new file (no local updated_at to preserve)
        await this.app.vault.create(filePath, articleContent);
        new Notice(`Created "${post.title}" in ${filePath}`);
      }
    } catch (error) {
      console.error("Error syncing selected post:", error);
      new Notice(`Error syncing post: ${error.message}`);
    }
  }
}
