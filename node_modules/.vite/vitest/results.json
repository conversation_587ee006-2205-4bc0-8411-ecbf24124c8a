{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 26.669375000000002, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 5.619959000000051, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.225459000000001, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 83.9011670000001, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 7.187250000000006, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.771000000000015, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 9.392375000000015, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 24.898042000000032, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.5232919999999694, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 27.768917000000044, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.764542000000006, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.885958000000016, "failed": false}], [":tests/basic.test.ts", {"duration": 2.73595899999998, "failed": false}]]}