{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 22.885917000000006, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.6784999999999854, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 90.54054099999996, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 7.030709000000002, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 5.7514999999999645, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.2832500000000095, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 9.422624999999982, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 25.28162500000002, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.122042000000022, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 28.351125000000025, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.747458999999992, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.18641600000001, "failed": false}], [":tests/basic.test.ts", {"duration": 2.890625, "failed": false}]]}