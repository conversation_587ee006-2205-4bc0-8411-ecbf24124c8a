{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 25.40599999999995, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 6.4363329999999905, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.765625, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 98.48966700000005, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 7.242250000000013, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.4519169999999804, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 14.522874999999999, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 25.953416000000004, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.834915999999964, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 30.370834000000002, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.8140000000000214, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.589083000000016, "failed": false}], [":tests/basic.test.ts", {"duration": 2.8302920000000427, "failed": false}]]}