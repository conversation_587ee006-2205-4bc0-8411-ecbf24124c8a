{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 39.397208000000035, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.265083000000004, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 90.515625, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 8.079416000000037, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 6.928875000000005, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 5.34537499999999, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 9.047124999999994, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 23.208709, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.792292000000032, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 29.775666, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.9171249999999986, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.356291999999996, "failed": false}], [":tests/basic.test.ts", {"duration": 2.5951670000000036, "failed": false}]]}