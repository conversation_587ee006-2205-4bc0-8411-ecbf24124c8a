{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 24.720249999999965, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 5.921457999999973, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.252207999999996, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 93.56491699999992, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 7.202541999999994, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.4415839999999776, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 8.878582999999992, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 25.933249999999987, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.561958000000004, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 29.761791000000017, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.7113339999999653, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.340167000000008, "failed": false}], [":tests/basic.test.ts", {"duration": 2.931624999999997, "failed": false}]]}