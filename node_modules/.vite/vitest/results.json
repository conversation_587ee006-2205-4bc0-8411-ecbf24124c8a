{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 23.525417000000004, "failed": false}], [":tests/sync/timestamp-sync.test.ts", {"duration": 6.064416999999992, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.180666000000031, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 87.09916699999997, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 6.913415999999984, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.372250000000008, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 8.604249999999979, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 23.318917, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.383207999999968, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 27.677540999999962, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 2.6595839999999953, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 18.529958000000022, "failed": false}], [":tests/basic.test.ts", {"duration": 2.8560829999999555, "failed": false}]]}