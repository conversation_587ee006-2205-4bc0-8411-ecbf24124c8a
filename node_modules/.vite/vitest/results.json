{"version": "2.1.9", "results": [[":tests/content-conversion.test.ts", {"duration": 38.37987500000003, "failed": false}], [":tests/utils/property-mapping.test.ts", {"duration": 5.122834000000012, "failed": false}], [":tests/components/GhostSyncView.vitest.test.ts", {"duration": 85.65808299999992, "failed": false}], [":tests/services/sync-status-service.test.ts", {"duration": 6.609042000000045, "failed": false}], [":tests/sync/sync-status.test.ts", {"duration": 3.6829589999999826, "failed": false}], [":tests/api/ghost-api.test.ts", {"duration": 9.438125000000014, "failed": false}], [":tests/components/StatusBadge.test.ts", {"duration": 24.197791999999993, "failed": false}], [":tests/views/svelte-sync-status-view.test.ts", {"duration": 6.578082999999992, "failed": false}], [":tests/services/obsidian-app-adapter.test.ts", {"duration": 27.735874999999965, "failed": false}], [":tests/components/component-basic.test.ts", {"duration": 3.430999999999983, "failed": false}], [":tests/components/PropertyDisplay.test.ts", {"duration": 20.993458000000032, "failed": false}], [":tests/basic.test.ts", {"duration": 2.6572079999999687, "failed": false}]]}